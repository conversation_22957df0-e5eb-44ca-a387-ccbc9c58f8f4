# Keycloak with PostgreSQL - Learning Setup

This project contains two different Docker Compose configurations for learning Keycloak with PostgreSQL.

## Files Overview

### 1. `docker-compose.yml` (Original)
- **Keycloak Port**: 8080
- **PostgreSQL Port**: 5432
- **Admin Tool**: pgAdmin (port 5050, profile-based)
- **Admin User**: admin/admin_password
- **Database**: keycloak/keycloak/keycloak_password

### 2. `docker-compose-alternative.yml` (Alternative)
- **Keycloak Port**: 8081
- **PostgreSQL Port**: 5433
- **Admin Tool**: Adminer (port 8082)
- **Admin User**: administrator/admin123!
- **Database**: keycloak_db/keycloak_user/secure_password123

### 3. `Dockerfile`
- Custom Keycloak image with additional tools
- Pre-built configuration for faster startup
- Debugging tools included

## Quick Start

### Option 1: Using the original configuration
```bash
# Start all services
docker-compose up -d

# Start with pgAdmin for database management
docker-compose --profile tools up -d

# View logs
docker-compose logs -f keycloak
```

### Option 2: Using the alternative configuration
```bash
# Start all services
docker-compose -f docker-compose-alternative.yml up -d

# View logs
docker-compose -f docker-compose-alternative.yml logs -f auth_server
```

### Option 3: Using custom Dockerfile
```bash
# Build custom image
docker build -t my-keycloak .

# Update docker-compose.yml to use your custom image
# Change: image: quay.io/keycloak/keycloak:23.0
# To: image: my-keycloak
```

## Access Points

### Original Setup
- **Keycloak Admin Console**: http://localhost:8080
- **PostgreSQL**: localhost:5432
- **pgAdmin**: http://localhost:5050 (when using --profile tools)

### Alternative Setup
- **Keycloak Admin Console**: http://localhost:8081
- **PostgreSQL**: localhost:5433
- **Adminer**: http://localhost:8082

## Learning Features Enabled

Both configurations include these learning-friendly features:
- Token exchange
- Fine-grained authorization
- Health and metrics endpoints
- Debug logging
- Development mode (relaxed security)

## Database Connection from External Tools

### Original Setup
- Host: localhost
- Port: 5432
- Database: keycloak
- Username: keycloak
- Password: keycloak_password

### Alternative Setup
- Host: localhost
- Port: 5433
- Database: keycloak_db
- Username: keycloak_user
- Password: secure_password123

## Useful Commands

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (fresh start)
docker-compose down -v

# View service status
docker-compose ps

# Access Keycloak container shell
docker exec -it keycloak_server bash
# or for alternative:
docker exec -it learning_keycloak bash

# Access PostgreSQL directly
docker exec -it keycloak_postgres psql -U keycloak -d keycloak
# or for alternative:
docker exec -it learning_postgres psql -U keycloak_user -d keycloak_db
```

## Customization Examples

### Adding Custom Themes
1. Create a `themes` directory
2. Add your custom theme files
3. Uncomment the themes volume mount in docker-compose.yml

### Database Initialization Scripts
1. Create a `sql-scripts` directory
2. Add `.sql` files for database initialization
3. Scripts will run automatically on first startup

### Custom Keycloak Configuration
1. Create a `keycloak-config` directory
2. Add custom configuration files
3. Uncomment the configuration volume mount

## Troubleshooting

- If ports are already in use, change the port mappings
- Check container logs: `docker-compose logs [service-name]`
- Ensure Docker has enough memory allocated
- Wait for health checks to pass before accessing services

## Learning Resources

- Keycloak Admin Console: Create realms, users, clients, and roles
- Database Tools: Explore the Keycloak database schema
- Health Endpoints: Monitor service status
- Metrics: Performance monitoring data
