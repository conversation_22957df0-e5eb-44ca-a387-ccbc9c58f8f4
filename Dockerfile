# Dockerfile for Learning Keycloak with PostgreSQL
# This Dockerfile demonstrates how to build a custom Keycloak image
# that can be used with PostgreSQL database

# Use the official Keycloak base image
FROM quay.io/keycloak/keycloak:23.0

# Set metadata
LABEL maintainer="Learning Project"
LABEL description="Custom Keycloak image for learning with PostgreSQL"
LABEL version="1.0"

# Switch to root user to install additional packages if needed
USER root

# Install additional tools that might be useful for learning/debugging
# These are optional but helpful for troubleshooting
RUN microdnf update -y && \
    microdnf install -y \
    curl \
    postgresql \
    vim-minimal \
    && microdnf clean all

# Create a directory for custom configurations
RUN mkdir -p /opt/keycloak/conf/custom

# Set environment variables for PostgreSQL connection
# Note: These can be overridden in docker-compose.yml or at runtime
ENV KC_DB=postgres
ENV KC_DB_URL_HOST=postgres
ENV KC_DB_URL_PORT=5432
ENV KC_DB_URL_DATABASE=keycloak
ENV KC_DB_USERNAME=keycloak
ENV KC_DB_PASSWORD=keycloak_password

# Keycloak configuration
ENV KC_HOSTNAME_STRICT=false
ENV KC_HOSTNAME_STRICT_HTTPS=false
ENV KC_HTTP_ENABLED=true
ENV KC_PROXY=edge
ENV KC_LOG_LEVEL=info
ENV KC_METRICS_ENABLED=true
ENV KC_HEALTH_ENABLED=true

# Admin user configuration
ENV KEYCLOAK_ADMIN=admin
ENV KEYCLOAK_ADMIN_PASSWORD=admin_password

# Copy custom configuration files if you have any
# COPY ./keycloak-custom.conf /opt/keycloak/conf/custom/

# Copy custom themes if you want to learn about theming
# COPY ./themes/ /opt/keycloak/themes/

# Switch back to keycloak user for security
USER keycloak

# Build the Keycloak configuration
# This optimizes the startup by pre-building the configuration
RUN /opt/keycloak/bin/kc.sh build \
    --db=postgres \
    --features=token-exchange,admin-fine-grained-authz

# Expose the default Keycloak port
EXPOSE 8080

# Health check to ensure Keycloak is running properly
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health/ready || exit 1

# Default command - can be overridden in docker-compose.yml
# Using start-dev for development/learning purposes
CMD ["/opt/keycloak/bin/kc.sh", "start-dev"]
