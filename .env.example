# Example environment variables for Keycloak and PostgreSQL
# Copy this file to .env and fill in your actual values

# PostgreSQL Database Configuration
POSTGRES_DB=keycloak
POSTGRES_USER=keycloak
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Keycloak Admin Configuration
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=your_secure_admin_password_here

# Keycloak Database Connection (usually same as PostgreSQL)
KC_DB_USERNAME=keycloak
KC_DB_PASSWORD=your_secure_postgres_password_here

# Optional: Additional Security Settings
# KC_HOSTNAME=localhost
# KC_HOSTNAME_PORT=8080

# Development vs Production Toggle
KEYCLOAK_DEVELOPMENT_MODE=true

# Logging Level (DEBUG, INFO, WARN, ERROR)
KC_LOG_LEVEL=info
