version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: learning_postgres
    restart: always
    environment:
      POSTGRES_DB: keycloak_db
      POSTGRES_USER: keycloak_user
      POSTGRES_PASSWORD: secure_password123
    volumes:
      - postgres_volume:/var/lib/postgresql/data
      - ./sql-scripts:/docker-entrypoint-initdb.d  # For custom init scripts
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    networks:
      - auth_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U keycloak_user -d keycloak_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Keycloak Authentication Server
  auth_server:
    image: quay.io/keycloak/keycloak:23.0
    container_name: learning_keycloak
    restart: always
    command: start-dev
    environment:
      # Database Configuration
      KC_DB: postgres
      KC_DB_URL: *************************************
      KC_DB_USERNAME: keycloak_user
      KC_DB_PASSWORD: secure_password123
      
      # Admin Configuration
      KEYCLOAK_ADMIN: administrator
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ADMIN_PASSWORD: admin123!
      
      # Development Settings
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_PROXY: edge
      
      # Logging
      KC_LOG_LEVEL: DEBUG
      
      # Enable useful features for learning
      KC_FEATURES: token-exchange,admin-fine-grained-authz,preview
    ports:
      - "8081:8080"  # Different port for comparison
    volumes:
      - ./custom-themes:/opt/keycloak/themes  # Custom themes directory
    networks:
      - auth_network
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Database Administration Tool
  db_admin:
    image: adminer:latest
    container_name: learning_adminer
    restart: always
    ports:
      - "8082:8080"
    networks:
      - auth_network
    depends_on:
      - db
    environment:
      ADMINER_DEFAULT_SERVER: db

# Network Configuration
networks:
  auth_network:
    driver: bridge
    name: keycloak_learning_net

# Persistent Storage
volumes:
  postgres_volume:
    name: learning_postgres_data
