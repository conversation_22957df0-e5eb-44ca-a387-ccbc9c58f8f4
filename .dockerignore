# Docker ignore file for Keycloak learning project

# Version control
.git
.gitignore
README.md

# Documentation
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Node modules if you have any frontend components
node_modules/
npm-debug.log

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# Build artifacts
target/
build/
dist/
